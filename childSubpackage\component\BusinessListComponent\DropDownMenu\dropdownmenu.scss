@import "../../../../template/menuhead/index.scss";

.head {
  position: relative;
  z-index: 99;
  background-color: #fff;
  margin: 0;
  padding: 0;
}

.container {
  z-index: 4;
  font-size: 14px;
  flex-direction: column;

  &.container_hd {
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    border-top: 2rpx solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    padding: 10rpx 4.13% 0; /* 31rpx / 750rpx = 4.13% */
    box-sizing: border-box;
    z-index: 998;

    text {
      width: 45.33%; /* 340rpx / 750rpx = 45.33% */
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: linear-gradient(90deg, #ffb2aa 0%, #e72410 100%);
      border-radius: 8rpx;
      color: #fff;
      font-size: 34rpx;
      font-family:
        <PERSON><PERSON>ang SC,
        PingFang SC-Semibold;
      font-weight: 600;
    }

    .reset {
      background: linear-gradient(74deg, #eeeeee 0%, #f5f5f5 100%);
      font-size: 34rpx;
      text-align: CENTER;
      color: #74798c;
    }
  }
}

.slidedown {
  transform: translateY(0%);
}

@keyframes slidown {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0%);
  }
}

.slidown {
  display: block;
  animation: slidown 0.2s ease-in both;
}

@keyframes slidup {
  from {
    transform: translateY(0%);
  }

  to {
    transform: translateY(-100%);
  }
}

.z-height {
  overflow-y: scroll;
  background: #fff;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.slidup {
  display: block;
  animation: slidup 0.2s ease-in both;
}

.disappear {
  display: none !important;
}

.show {
  display: block;
}

.line-tesu {
  position: relative;

  &::after {
    content: " ";
    width: 100%;
    height: 1px;
    background: #eee;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.5);
  }
}

.nav-child {
  &.active {
    .nav-title {
      color: #e72410;
    }

    .icon {
      border-bottom: 4px solid #e72410;
      border-top: 0;
    }
  }
}

.active > input {
  color: #e72410;
}
