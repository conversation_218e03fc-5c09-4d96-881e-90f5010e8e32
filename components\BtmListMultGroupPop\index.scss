/* components/BtmListMultGroupPop/index.scss */

/* 自定义弹窗样式 */
.custom-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;
  }
}

.custom-popup-container {
  width: 100%;
  background-color: #fff;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.custom-popup-mask.show .custom-popup-container {
  transform: translateY(0);
}

.btm-list-wrap {
  background-color: #fff;
  position: relative;
  overflow: hidden;
  height: 916rpx; /* 调整高度：Tab 100rpx + 内容 816rpx = 916rpx */
  display: flex;
  flex-direction: column;
}

/* 底部按钮样式 */
.popup-footer {
  display: flex;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin: 0 12rpx;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;

  &:active {
    background-color: #e8e8e8;
  }
}

.confirm-btn {
  background-color: #e72410;
  color: #fff;

  &:active {
    background-color: #d01e0a;
  }

  &.disabled {
    background: rgba(231, 36, 16, 0.08) !important;
    color: rgba(231, 36, 16, 0.5) !important;
    cursor: not-allowed;

    &:active {
      background: rgba(231, 36, 16, 0.08) !important;
    }
  }
}

/* 移除不需要的原有样式，保留基础容器样式 */

/* Tab 头部样式 - 参考 chainMap 样式 */
.tab-header {
  width: 100%;
  background: #ffffff;
  box-sizing: border-box;
  height: 100rpx;
  border-bottom: 1rpx solid #eee;
  padding: 0 24rpx;
  flex-shrink: 0;

  .tabs-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12rpx;
    height: 100%;

    .tab-item {
      position: relative;
      width: 202rpx;
      text-align: center;
      border-radius: 8rpx;
      transition: all 0.3s ease;
      height: 56rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      // 默认样式
      font-weight: 400;
      font-size: 28rpx;
      color: #74798c;

      .vip {
        position: absolute;
        width: 58rpx;
        height: 40rpx;
        right: -12rpx;
        top: -22rpx;
      }

      // 选中样式
      &.active {
        background: linear-gradient(90deg, #e72410 0%, #f17b6f 100%);
        border-radius: 8rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #ffffff;
      }
    }
  }
}

/* 针对产业链图谱样式 */
.chainMap {
  .tab-item {
    &.active {
      background: linear-gradient(90deg, #c2a5ff 0%, #8441ff 100%) !important;
    }
  }
}

/* 内容容器样式 */
.select-container {
  width: 100%;
  position: relative;
  overflow: hidden;
  height: 816rpx; /* 恢复固定高度：900rpx - 84rpx = 816rpx */
  display: flex;
  flex-direction: column;
}

.select-wrap {
  height: 816rpx; /* 恢复固定高度 */
  width: 100%;
  display: flex;
  font-weight: 400;
  font-size: 26rpx;
  color: #525665;
}

.select-wrap .list {
  height: 816rpx; /* 恢复固定高度 */
  overflow-y: auto;
}

.select-wrap .list .item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 24rpx;
  box-sizing: border-box;
}

.parent-list {
  background-color: rgba(247, 247, 247, 0.6);
  width: 38.4% !important;
  flex-shrink: 0;
  height: 816rpx; /* 恢复固定高度 */
}

.parent-list .item.active {
  background-color: #fff;
}

.parent-list .item.selected {
  background-color: #fff;
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.child-list {
  background-color: #fff;
  flex: 1;
  position: relative;
}

.child-list .item.selected {
  font-weight: 600;
  font-size: 26rpx;
  color: #e72410;
}

.selected-text {
  color: #e72410 !important;
  font-size: 26rpx;
  font-weight: 600 !important;
}

.checkmark {
  width: 32rpx;
  height: 32rpx;
  flex-shrink: 0;
}

.checkmark.show {
  opacity: 1;
}

.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
