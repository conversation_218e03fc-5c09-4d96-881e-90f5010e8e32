// 通用样式
.text-num-ellipsis {
  -webkit-line-clamp: 2;
}

// 内容区域样式
.content {
  position: relative;
  width: 100%;
  background: #f7f7f7;
}

.main-content {
  position: relative;
}

// 头部区域
.h_head {
  position: relative;
  z-index: 12;
  width: 100%;
  height: 312rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

// 搜索区域
.search_wrap {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 24rpx;
  padding: 0 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .h_head_input {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72rpx;
    background: #ffffff;
    border-radius: 8rpx;
    box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(7, 110, 228, 0.1);
    padding: 0 0 0 28rpx;
    box-sizing: border-box;
    font-size: 28rpx;
    font-family:
      PingFang SC,
      PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #9b9eac;

    .h_search {
      min-width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
    }

    .texts {
      font-weight: 400;
      font-size: 28rpx;
      color: #9b9eac;
      width: 100%;
    }
  }
}

// 筛选器样式
.filter {
  flex-shrink: 0;
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  padding-right: 24rpx;
  margin-left: 24rpx;
}

// 企业功能网格样式
.zt-container {
  padding: 40rpx 42rpx;
  background: #fff;
  height: 376rpx;

  .zt-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); // 一行4个
    grid-template-rows: repeat(2, 1fr); // 一共2行
    gap: 32rpx 62rpx;

    .zt-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 120rpx;
      height: 132rpx;

      .zt-image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-bottom: 8rpx;
        overflow: hidden;
      }

      .zt-name {
        font-weight: 400;
        font-size: 24rpx;
        color: #20263a;
        line-height: 32rpx;
        text-align: center;
      }
    }
  }
}

// 重点企业模块样式
.enterprise-section {
  margin-top: 20rpx;

  .enterprise-card {
    background: #fff;
    width: 100vw;
    position: relative;

    .card-title {
      font-weight: 600;
      font-size: 32rpx;
      color: #20263a;
      position: relative;
      height: 92rpx;
      display: flex;
      align-items: center;
      padding-left: 24rpx;

      // 标题下方分割线
      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1rpx;
        background: #eeeeee;
      }
    }

    .enterprise-content {
      padding: 0 0 32rpx;

      .enterprise-wrap {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 32rpx 24rpx 0;
        gap: 18rpx;
        transition: all 0.3s ease;

        .enterprise-item {
          position: relative;
          width: 342rpx;
          height: 140rpx;
          background: linear-gradient(180deg, #f7f7f7 0%, #ffffff 100%);
          border-radius: 12rpx;
          border: 1rpx solid #eeeeee;
          padding: 24rpx;
          box-sizing: border-box;
          overflow: hidden;
          transition: all 0.2s ease;

          // 企业类型名称
          .enterprise-item-name {
            font-weight: 400;
            font-size: 28rpx;
            color: #525665;
            word-break: break-all;
            margin-bottom: 16rpx;
          }

          // 数量信息
          .enterprise-item-count {
            font-weight: 600;
            font-size: 32rpx;
            color: #20263a;

            .enterprise-item-unit {
              font-weight: 400;
              font-size: 24rpx;
              color: #9b9eac;
              margin-left: 4rpx;
            }
          }

          // 背景装饰图片
          .enterprise-item-bg {
            position: absolute;
            bottom: 16rpx;
            right: 16rpx;
            width: 56rpx;
            height: 56rpx;
            z-index: 1;
          }
        }

        // hover效果类
        .enterprise-item-active {
          transform: scale(0.98) !important;
          opacity: 0.9 !important;
        }

        // 如果是奇数个，最后一个居左
        .enterprise-item:nth-child(odd):last-child {
          margin-right: auto;
        }
        // 加载动画
        .enterprise-item {
          animation: fadeInUp 0.6s ease forwards;
          // 初始状态
          &:not(.active) {
            opacity: 0;
            transform: translateY(20rpx);
          }

          @for $i from 1 through 10 {
            &:nth-child(#{$i}) {
              animation-delay: #{($i - 1) * 0.1}s;
            }
          }
        }
      }
      @keyframes fadeInUp {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      // 集团模块专用样式
      .group-wrap {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        padding: 32rpx 24rpx 0;
        gap: 18rpx;

        .group-item {
          position: relative;
          width: 222rpx;
          height: 160rpx;
          background: linear-gradient(180deg, #f7f7f7 0%, #ffffff 100%);
          border-radius: 12rpx;
          border: 1rpx solid #eeeeee;
          padding: 20rpx;
          box-sizing: border-box;
          overflow: hidden;
          transition: all 0.2s ease;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          // 集团名称 - 顶部
          .group-item-name {
            font-weight: 400;
            font-size: 28rpx;
            color: #20263a;
            word-break: break-all;
            align-self: flex-start;
          }
          // 数量信息 - 底部
          .group-item-count {
            font-weight: 600;
            font-size: 32rpx;
            color: #20263a;
            align-self: flex-start;
            text {
              font-weight: 400;
              font-size: 24rpx;
              color: #9b9eac;
              padding-left: 4rpx;
            }
          }

          // 背景装饰图片
          .group-item-bg {
            position: absolute;
            bottom: 20rpx;
            right: 12rpx;
            width: 56rpx;
            height: 56rpx;
          }
        }

        // hover效果类
        .group-item-active {
          transform: scale(0.98) !important;
          opacity: 0.9 !important;
        }
      }

      // 资本模块专用样式
      .capital-wrap {
        display: flex;
        padding: 32rpx 24rpx 0;
        gap: 18rpx;

        // 左边大卡片
        .capital-left-item {
          position: relative;
          flex: 1;
          height: 310rpx;
          background: rgba(47, 124, 255, 0.08);
          border-radius: 12rpx;
          padding: 28rpx;
          box-sizing: border-box;
          overflow: hidden;
          transition: all 0.2s ease;
          display: flex;
          flex-direction: column;

          // 主标题
          .capital-left-title {
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            color: #2f7cff;
          }

          // 副标题
          .capital-left-subtitle {
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #94a4c0;
            align-self: flex-start;
          }

          // 右下角装饰图片
          .capital-left-bg {
            position: absolute;
            bottom: 12rpx;
            right: 12rpx;
            width: 152rpx;
            height: 152rpx;
          }
        }

        // 右边容器
        .capital-right-container {
          display: flex;
          flex-direction: column;
          gap: 18rpx;

          // 右边小卡片通用样式
          .capital-right-item {
            position: relative;
            width: 344rpx;
            height: 146rpx;
            border-radius: 12rpx;
            padding: 28rpx;
            box-sizing: border-box;
            overflow: hidden;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;

            // 标题通用样式
            .capital-right-title {
              font-family:
                PingFang SC,
                PingFang SC;
              font-weight: 600;
              font-size: 28rpx;
            }

            // 右下角图片
            .capital-right-bg {
              position: absolute;
              bottom: 12rpx;
              right: 12rpx;
              width: 120rpx;
              height: 120rpx;
            }
          }

          // 第一个小卡片
          .capital-right-item-1 {
            background: rgba(255, 98, 82, 0.08);

            .capital-right-title-1 {
              color: #fe5b4a;
            }
          }

          // 第二个小卡片
          .capital-right-item-2 {
            background: rgba(255, 147, 46, 0.08);

            .capital-right-title-2 {
              color: #ff932e;
            }
          }
        }
        .capital_line {
          width: 68rpx;
          height: 8rpx;
          transform: translateY(-6rpx);
        }

        // hover效果类
        .capital-item-active {
          transform: scale(0.98) !important;
          opacity: 0.9 !important;
        }
      }
    }
  }
}
