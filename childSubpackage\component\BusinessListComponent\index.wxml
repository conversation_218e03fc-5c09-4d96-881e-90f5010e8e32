<view class="business-list-component">
  <view class="page_head">
    <!-- 下拉菜单 -->
    <DropDownMenu
      dropDownMenuTitle="{{dropDownMenuTitle}}"
      dropDownMenuConfig="{{computedDropDownMenuConfig}}"
      class="drop-menu"
      bindsubmit="onFlitter"
      bindvip="vipPop"
      externalFilterState="{{externalFilterState}}"
      useIndustrialList="{{useIndustrialList}}"
      chain_code="{{chain_code}}"
      purchased="{{purchased}}"
    />
    <view class="company_num">
      <view class="text_left"
        >共找到<text class="color_num">{{company_num}}</text
        >家企业</view
      >
      <view wx:if="{{showMapMode}}" class="ditu_mode" catchtap="goMapMode">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/in_dituico.png"
          class="img"
        />
        <text>地图模式</text>
      </view>
    </view>
  </view>

  <!-- 企业列表 -->
  <view class="card-box" style="height: {{computedHeight}}px;">
    <refresh-scroll
      id="refresh-scroll"
      container-height="{{computedHeight}}"
      request-url="{{requestFunction}}"
      request-params="{{requestParams}}"
      auto-load="{{false}}"
      empty-text="暂无企业数据"
      empty-tip=""
      bind:datachange="onDataChange"
      bind:scrolltolower="onScrollToLower"
      bind:scroll="onScroll"
    >
      <view slot="content" style="scale: 1;">
        <ComCard
          wx:for="{{requestData}}"
          wx:key="index"
          obj="{{item}}"
          btn="collect"
          bindcardFun="onCardAction"
          bindhandleTit="handleTit"
          hcard="custom-card-style"
          isCheckVip="{{isCheckVip}}"
        />
      </view>
    </refresh-scroll>
  </view>

  <Contact
    visible="{{showContact}}"
    entId="{{activeEntId}}"
    bindclose="onContactClose"
  ></Contact>

  <!-- VIP弹窗 -->
  <VipPop
    visible="{{vipVisible}}"
    bindclose="vipPop"
  ></VipPop>

  <!-- 地址弹窗 -->
  <dialog
    visible="{{showAddress}}"
    title="地址"
    isShowConfirm="{{false}}"
    showFooter="{{false}}"
  >
    <view class="dialog-con">
      <view style="padding: 0 50rpx;">
        <map
          id="map"
          longitude="{{location.lon}}"
          latitude="{{location.lat}}"
          markers="{{addmarkers}}"
          scale="{{11}}"
          style="width: 100%; height: 306rpx;"
        >
        </map>
      </view>
      <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
      <view bindtap="goMap" class="map"> 导航 </view>
      <view class="cancel" bindtap="onCloseAddress"> 取消 </view>
    </view>
  </dialog>
  <van-toast id="van-toast" />
</view>
