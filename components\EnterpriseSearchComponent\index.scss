.enterprise-search-component {
  .drop-menu {
    position: relative;
    z-index: 2;
  }

  .enterprise-list {
    .tip {
      padding: 20rpx 30rpx;
      font-size: 28rpx;
      color: #666;
      background: #f7f7f7;

      .count {
        color: #ff6b35;
        font-weight: bold;
      }
    }

    .card-list {
      padding: 0 20rpx;
      background: #f7f7f7;
      min-height: 100%;
    }

    .loading-more,
    .no-more {
      text-align: center;
      padding: 30rpx;
      font-size: 28rpx;
      color: #999;
    }

    .vip-limit {
      margin-top: 20rpx;
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400rpx;
      font-size: 32rpx;
      color: #999;
      background: #f7f7f7;
    }
  }

  .dialog-con {
    padding: 40rpx;
    text-align: center;

    .map {
      background: #007aff;
      color: white;
      padding: 20rpx 40rpx;
      border-radius: 10rpx;
      margin: 20rpx 0;
      display: inline-block;
    }

    .cancel {
      background: #f0f0f0;
      color: #333;
      padding: 20rpx 40rpx;
      border-radius: 10rpx;
      display: inline-block;
    }
  }
}
