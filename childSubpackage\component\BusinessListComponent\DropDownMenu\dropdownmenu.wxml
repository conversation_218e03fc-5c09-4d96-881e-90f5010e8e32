<import src="/template/menuhead/index"></import>
<view class="head">
  <view class="head_nav">
    <template
      is="menu-head"
      data="{{selected_source_name,dropDownMenuTitle,district_open,source_open,filter_open,selected_filter_name,selected_source_name,district_val,source_val,filter_val,containerWidth }}"
    ></template>
  </view>
</view>

<!-- 具体内容 - 根据配置动态显示 -->
<!-- 地区选择 -->
<region-selection
  wx:if="{{menuConfig.region.enabled}}"
  visible="{{menuConfig.region.position === 0 ? district_open : (menuConfig.region.position === 1 ? source_open : filter_open)}}"
  top="{{top}}"
  bindsubmit="getRegion"
  bindclose="closeRegion"
  oldData="{{regionData.code ? [regionData] : []}}"
/>

<!-- 产业链选择 -->
<SingleSelect
  wx:if="{{menuConfig.industry.enabled}}"
  visible="{{menuConfig.industry.position === 0 ? district_open : (menuConfig.industry.position === 1 ? source_open : filter_open)}}"
  top="{{top}}"
  defaultCode="{{selectChainVal}}"
  bindsubmit="handleSelectChain"
  bindclose="closeRegion"
></SingleSelect>

<!-- 更多筛选 - 使用ConfigurableForm -->
<view
  wx:if="{{menuConfig.filter.enabled}}"
  class="container container_hd line-tesu {{(menuConfig.filter.position === 0 ? district_open : (menuConfig.filter.position === 1 ? source_open : filter_open)) ? 'show' : 'disappear'}}"
  style="height: calc(100vh - {{top}}px); top: {{topNav}}px; display: flex; flex-direction: column;"
>
  <view
    style="height: calc(100vh - {{top}}px); overflow: hidden; padding-bottom: {{isIphoneX?'85px':'55px'}}; box-sizing: border-box;"
    catchtap="preventBubble"
  >
    <view class="configurable-form-wrapper">
      <ConfigurableForm
        variant="full"
        excludeFields="{{['ent_name', 'area_code_list', 'industry_code_list', 'industrial_list']}}"
        wrapHeight="calc(100vh - {{top}}px - {{isIphoneX?'85px':'55px'}})"
        bindsubmit="onConfigurableFormSubmit"
        bindvip="onConfigurableFormVip"
        bindready="onConfigurableFormReady"
        id="s-hunt"
      />
    </view>
  </view>
  <view
    class="footer"
    style="height: {{isIphoneX?'85px':'55px'}}; flex-shrink: 0;"
  >
    <text class="reset" bindtap="resetMoreFilter">重置</text>
    <text bindtap="confirmMoreFilter">确定</text>
  </view>
</view>
