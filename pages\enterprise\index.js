const app = getApp();

Page({
  data: {
    isLogin: app.isLogin(), // 是否登录
    // 企业功能数据
    enterpriseData: [
      {
        id: 1,
        name: '企业定位器',
        icon: '/image/report/e_qydwq.png',
        url: '/companyPackage/pages/searTerm/sear-term'
      },
      {
        id: 2,
        icon: '/image/report/e_fjqy.png',
        name: '附近企业',
        url: '/pages/hIndusty/index'
      },
      {
        id: 3,
        icon: '/image/report/e_cgx.png',
        name: '找关系',
        url: '/companyPackage/pages/merchantsMap/merchants'
      },
      {
        id: 4,
        icon: '/image/report/e_qytp.png',
        name: '企业图谱',
        url: '/companyPackage/pages/authoritativeList/authoritativeList'
      },
      {
        id: 5,
        icon: '/image/report/e_qybd.png',
        name: '企业榜单',
        url: '/pages/companyInfo/companyInfo'
      },
      {
        id: 6,
        icon: '/image/report/e_wzqy.png',
        name: '外资企业',
        url: '/subPackage/pages/financing/home/<USER>'
      },
      {
        id: 7,
        name: '查看应链',
        icon: '/image/report/e_ckgyl.png',
        url: '/companyPackage/pages/mineRelation/relation'
      },
      {
        id: 8,
        name: '我的收藏',
        icon: '/image/report/e_wdsc.png',
        url: '/subPackage/pages/searchPark/index'
      }
    ],
    // 重点企业数据（模拟数据）
    industryData: [
      {
        name: '上市企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent1.png',
        count: 1256,
        key: 'listed_ent_cnt'
      },
      {
        name: '小巨人',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent2.png',
        count: 3428,
        key: 'little_giant'
      },
      {
        name: '单项冠军',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent3.png',
        count: 892,
        key: 'single_champ'
      },
      {
        name: '高新技术企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent4.png',
        count: 15672,
        key: 'high_tech_ent_cnt'
      },
      {
        name: '专精特新企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent5.png',
        count: 8934,
        key: 'specialized_ent_cnt'
      },
      {
        name: '创新型中小企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent6.png',
        count: 12456
      },
      {
        name: '科技型企业',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_ent7.png',
        count: 6789,
        key: 'technology_ent_cnt'
      }
    ],
    // 集团数据
    groupData: [
      {
        name: '央企',
        img: '/image/report/e_yq.png',
        count: 23,
        key: 'central_group'
      },
      {
        name: '国企',
        img: '/image/report/e_gq.png',
        count: 341,
        key: 'state_group'
      },
      {
        name: '民企',
        img: '/image/report/e_mq.png',
        count: '999+',
        key: 'private_group'
      }
    ],
    // 资本数据
    capitalData: [
      {
        name: '融资快讯',
        subtitle: '实时抓取投融资舆情',
        img: '/image/report/e_rzkx.png',
        line: '/image/report/e_tl1.png',
        count: 1567,
        key: 'vc_capital'
      },
      {
        name: '投资机构',
        img: '/image/report/e_tzjg.png',
        line: '/image/report/e_tl2.png',
        count: 892,
        key: 'pe_capital'
      },
      {
        name: '政府引导基金',
        img: '/image/report/e_zfydjj.png',
        line: '/image/report/e_tl3.png',
        count: 456,
        key: 'industry_fund'
      }
    ]
  },
  onShow: function () {
    const isLogin = app.isLogin();
    this.setData({
      isLogin
    });
    // 根据登录状态更新数据显示
    this.updateDataByLoginStatus(isLogin);
  },

  // 根据登录状态更新数据显示
  updateDataByLoginStatus(isLogin) {
    if (!isLogin) {
      // 未登录时，将所有数据的count设置为0
      const industryData = this.data.industryData.map(item => ({
        ...item,
        count: 0
      }));
      const groupData = this.data.groupData.map(item => ({
        ...item,
        count: 0
      }));
      const capitalData = this.data.capitalData.map(item => ({
        ...item,
        count: 0
      }));

      this.setData({
        industryData,
        groupData,
        capitalData
      });
    } else {
      // 已登录时，恢复原始数据（这里可以调用接口获取真实数据）
    }
  },

  // 处理搜索点击事件
  async handleClick(e) {
    const {isLogin} = this.data;
    const constant = e.currentTarget.dataset?.type;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    if (constant === 'search') {
      // 跳转到企业搜索页面
      app.route(this, '/companyPackage/pages/searTerm/sear-term');
    } else if (constant === 'filter') {
      // 跳转到多维搜索页面（可以根据实际需求修改跳转地址）
      app.route(this, '/companyPackage/pages/searTerm/sear-term');
    }
  },

  // 处理企业功能点击事件
  async onEnterpriseClick(e) {
    const {isLogin} = this.data;
    const {item} = e.currentTarget.dataset;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    // 跳转到对应页面
    app.route(this, item.url);
  },

  // 处理重点企业点击事件
  async onEnterpriseItemClick(e) {
    const {isLogin} = this.data;
    const {item} = e.detail;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    // 跳转到企业列表页面
    const url = `/companyPackage/pages/searTerm/sear-term?type=${
      item.key
    }&title=${encodeURIComponent(item.name)}`;
    app.route(this, url);
  },

  // 处理集团点击事件
  async onGroupItemClick(e) {
    const {isLogin} = this.data;
    const {item} = e.detail;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    const url = `/companyPackage/pages/searTerm/sear-term?type=${
      item.key
    }&title=${encodeURIComponent(item.name)}`;
    app.route(this, url);
  },

  // 处理资本点击事件
  async onCapitalItemClick(e) {
    const {isLogin} = this.data;
    const {item} = e.detail;

    if (!isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    // 跳转到资本搜索页面
    const url = `/companyPackage/pages/searTerm/sear-term?type=${
      item.key
    }&title=${encodeURIComponent(item.name)}`;
    app.route(this, url);
  }
});
