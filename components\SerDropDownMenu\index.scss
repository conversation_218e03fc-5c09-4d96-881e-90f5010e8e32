@import "../../template/menuhead/index.scss";

.head {
  position: relative;
  z-index: 2;
}

.container {
  position: relative;
  z-index: 4;
  font-size: 14px;
}

.slidedown {
  transform: translateY(0%);
}

@keyframes slidown {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0%);
  }
}

.slidown {
  display: block;
  animation: slidown 0.2s ease-in both;
}

@keyframes slidup {
  from {
    transform: translateY(0%);
  }

  to {
    transform: translateY(-100%);
  }
}

.z-height {
  overflow-y: scroll;
  background: #fff;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.slidup {
  display: block;
  animation: slidup 0.2s ease-in both;
}

.disappear {
  display: none;
}

.show {
  display: block;
}

.container_hd {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow-y: scroll;
  background-color: rgba(0, 0, 0, 0.5);
}

.line-tesu {
  position: relative;
}

.line-tesu::after {
  content: " ";
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.nav-child.active .nav-title {
  color: #e72410;
}

.nav-child.active .icon {
  border-bottom: 4px solid #e72410;
  border-top: 0;
}

/* 筛选相关 */
/* companyPackage/pages/companyListFilter/companyListFilter.scss */

.container .footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  height: 85px;
  background-color: #fff;
  border-top: 2rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  padding: 10rpx 31rpx 0;
}

.container .footer text {
  width: 340rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(90deg, #ffb2aa 0%, #e72410 100%);
  border-radius: 8rpx;
  color: #fff;
  font-size: 34rpx;
  font-family:
    PingFang SC,
    PingFang SC-Semibold;
  font-weight: 600;
}

.container .footer .reset {
  background: linear-gradient(74deg, #eeeeee 0%, #f5f5f5 100%);
  font-size: 34rpx;
  text-align: CENTER;
  color: #74798c;
}

.active > input {
  color: #e72410;
}

/* 产业链弹窗样式 */
.industrial-popup {
  position: fixed;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  bottom: 0;

  .industrial-content {
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .industrial-header {
    padding: 30rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    border-bottom: 1rpx solid #eee;
  }

  .industrial-list {
    flex: 1;
    max-height: 60vh;
    overflow-y: auto;
    padding: 20rpx 0;
  }

  .industrial-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    &.selected {
      background: #fff2f0;
      color: #e72410;
    }

    text {
      font-size: 28rpx;
      color: #333;
    }

    &.selected text {
      color: #e72410;
      font-weight: 600;
    }
  }

  .check-icon {
    width: 32rpx;
    height: 32rpx;
  }

  .industrial-footer {
    display: flex;
    padding: 30rpx;
    gap: 20rpx;
    border-top: 1rpx solid #eee;

    button {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;

      &.btn-cancel {
        background: #f5f5f5;
        color: #666;
      }

      &.btn-confirm {
        background: #e72410;
        color: #fff;
      }
    }
  }
}
