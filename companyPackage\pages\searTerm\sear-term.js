// 使用新的 BtmListMultGroupPop 渲染工具替代 hunt 组件
import {searRender} from './utils';

// 从 ConfigurableForm 引入相关方法
import {
  clearChildComponent,
  fillChildComponent,
  closeEntNamePop
} from '../../../components/ConfigurableForm/utils/component-helpers';

import {hijack} from '../../../utils/route';
import {handleShareUrl, getShareUrl} from '../../../utils/mixin/pageShare';
var checkMixin = require('../../../utils/mixin/check');
import {home} from '../../../service/api.js';
import {commonRequest} from '../../../utils/mixin/loginbeforeReq';
import {hasPrivile} from '../../../utils/route';
const {
  SEARCH_TERM_LIST
} = require('../../../components/ConfigurableForm/config/constants');

const app = getApp();

Page({
  behaviors: [checkMixin()],
  data: {
    isIphoneX: app.globalData.isIphoneX,
    // /------------------/
    saveSearVal: '', //保存选项值
    // 接收到的参数
    isHeight: false,
    paramsData: {},
    // 模版
    templateAry: [],
    updating: false, // 数据是否处于更新状态
    renderAry: [] //模版渲染数组
  },
  onLoad(options) {
    const {title} = options;
    if (title) {
      wx.setNavigationBarTitle({
        title
      });
    }
    handleShareUrl();
  },
  onShow() {
    //公共弹窗的请求
    commonRequest();
  },
  handleLogin() {
    // 获取搜索模版
    this.getsearData();
  },
  getParams(e) {
    // 筛选后获取的数据
    let {isHeight, paramsData} = e.detail;
    // console.log(2222222, paramsData);
    this.setData({
      isHeight,
      paramsData
    });
  },
  /*--模版相关--*/
  onsearPop: hijack(
    async function () {
      // 搜索模版-弹窗
      let permission = await hasPrivile({
        packageType: true
      });
      if (permission == '普通VIP') {
        this.setData({
          vipVisible: true
        });
        return;
      }
      this.setData({
        searPop: true
      });
    },
    {
      type: 'searterm',
      app: app
    }
  ),
  async getsearData() {
    let {items} = await home.searTemplate();
    let searRenderObj = {};
    if (!items) {
      // app.showToast('网络丢失,请稍后再试!')
      items = [];
      console.warn('获取数据失败!');
      return;
    }
    items.map((item, index) => {
      item.active = false;
      if (index === 0) {
        item.active = true;
        searRenderObj = JSON.parse(item['search_json'] || null);
        if (searRenderObj?.industrial_list?.length) {
          searRenderObj.industrial_list = searRenderObj.industrial_list.map(
            ({chain_code, code = '', ...rest}) => ({
              ...rest,
              code: chain_code || code
            })
          );
        }
      }
      return item;
    });
    let renderAry = searRender(searRenderObj);
    this.setData({
      templateAry: items,
      renderAry
    });
  },

  onsearItm(e) {
    //搜索模板高亮切換
    let {templateAry} = this.data;
    const {
      item: {search_json},
      index
    } = e.currentTarget.dataset;
    let renderObj = JSON.parse(search_json || null);
    let idx = templateAry.findIndex(i => i.active);
    if (index != idx) {
      templateAry[idx].active = false;
      templateAry[index].active = true;
    }
    // 兼容app
    this.lanJieData(renderObj, true);
    let renderAry = searRender(renderObj);
    // 設置企業规模显示的数据
    this.setData({
      templateAry,
      renderAry
    });
  },
  async onsearDel(e) {
    //搜索模版-删除
    const {item, index} = e.currentTarget.dataset;
    const {id} = item;
    let {templateAry} = this.data;
    try {
      const res = await home.delTemplate(id);
      // 删除成功
      if (templateAry[index].active && templateAry.length >= 2) {
        //恰好删除的是高亮的这个

        let item, idx;
        if (index === 0) {
          item = templateAry[1];
          idx = 1;
        } else {
          item = templateAry[0];
          idx = 0;
        }
        this.onsearItm({
          currentTarget: {
            dataset: {
              item,
              index: idx
            }
          }
        });
      }
      templateAry.splice(index, 1);
      this.setData({
        templateAry
      });
    } catch (err) {
      console.log(err);
      app.showToast('删除失败,请稍后再试!');
    }
  },
  clearSear: hijack(
    function () {
      // 清空模版
      clearChildComponent(this, '#sear-hunt')();
    },
    {
      type: 'searterm',
      app: app
    }
  ),
  saveSear: hijack(
    function () {
      const {isHeight} = this.data;
      closeEntNamePop(this, '#sear-hunt')();
      if (isHeight) {
        this.setData({
          saveSearPop: true,
          saveSearVal: '',
          updating: false
        });
      } else {
        app.showToast('请选择筛选条件！', 'none', 1500);
      }
    },
    {
      type: 'searterm',
      app: app
    }
  ),
  saveSearCancel() {
    //取消
    this.setData({
      saveSearPop: false
    });
  },
  async saveSearSub() {
    //保存-确定
    let {saveSearVal, paramsData, updating} = this.data;
    if (updating) return;
    saveSearVal = saveSearVal.trim();
    if (!saveSearVal) {
      app.showToast('请输入模版名称!');
      return;
    }
    // 针对app做一次拦截 -数据处理 和app保持一致
    this.lanJieData(paramsData);
    let copyObj = {
      title: saveSearVal,
      search_json: JSON.stringify(paramsData)
    };
    this.setData({
      updating: true
    });
    try {
      await home.saveTemplate(copyObj);
      // 发请求 关闭弹窗
      this.saveSearCancel();
      app.showToast('模版新增完成!', 'none', 1500);
      this.getsearData(); //发送请求刷新数据
    } catch (err) {
      app.showToast('新增模版失败,请稍后再试!', 'none', 1500);
      console.log(err);
      this.saveSearCancel();
    }
  },
  submitSear() {
    //立即搜索--直接到搜索
    let obj;
    const {paramsData, isHeight} = this.data;
    closeEntNamePop(this, '#sear-hunt')();
    if (paramsData['est_date'].length) {
      paramsData['est_date'].map(i => {
        delete i['name'];
      });
    }
    obj = JSON.stringify(paramsData);
    if (!isHeight) {
      app.showToast('请先选择筛选条件!');
      return;
    }
    obj = encodeURIComponent(obj);
    app.route(this, `/childSubpackage/pages/huntList/searchs?str=${obj}`);
  },
  searsub() {
    let {templateAry} = this.data,
      tempObj;
    if (templateAry.length <= 0) return;

    let {search_json} = templateAry.filter(item => item.active)[0];
    tempObj = JSON.parse(search_json);
    this.lanJieData(tempObj, true);
    // 修复时间处理逻辑
    fillChildComponent(this, '#sear-hunt')(tempObj);
  },
  lanJieData(mapObj, back) {
    // 返回的时候拿到我们自己需要的数据结构
    if (mapObj?.industrial_list?.length) {
      mapObj.industrial_list = mapObj.industrial_list.map(
        ({chain_code, code = '', ...rest}) => {
          const processedItem = {
            ...rest,
            code: chain_code || code
          };

          // 确保有 tabCode 字段（用于回显时切换到正确的Tab）
          if (!processedItem.tabCode) {
            // 根据数据特征推断 tabCode
            if (processedItem.type === 'classic' || processedItem.classic) {
              processedItem.tabCode = 'classic';
            } else if (processedItem.type === 'hot' || processedItem.hot) {
              processedItem.tabCode = 'hot';
            } else {
              processedItem.tabCode = 'chainMap'; // 默认为产业链图谱
            }
          }

          return processedItem;
        }
      );
    }
    if (!back) return;
    // 弹窗模板回显兼容处理
    try {
      // 时间 用当前时间戳 并且加上special
      const estDateField = SEARCH_TERM_LIST.find(
        field => field.type === 'est_date'
      );
      if (
        estDateField &&
        estDateField.list &&
        mapObj['est_date'] &&
        mapObj['est_date'].length
      ) {
        // 直接遍历原数组进行修改
        mapObj['est_date'].forEach(item => {
          let found = false;
          estDateField.list.forEach(itm => {
            if (item.name === itm.name) {
              item.start = itm.id.split('$')[0];
              item.end = itm.id.split('$')[1];
              found = true;
            }
          });
          if (!found) {
            item.special = true;
          }
        });
      }
      // 处理注册资本
      const reGDateField = SEARCH_TERM_LIST.find(
        field => field.type === 'reg_capital'
      );
      if (
        reGDateField &&
        reGDateField.list &&
        mapObj['reg_capital'] &&
        mapObj['reg_capital'].length
      ) {
        mapObj['reg_capital'].forEach(item => {
          let found = false;
          reGDateField.list.forEach(itm => {
            if (item.name === itm.name) {
              item.start = itm.id.split('$')[0];
              item.end = itm.id.split('$')[1];
              found = true;
            }
          });
          if (!found) {
            item.special = true;
          }
        });
      }
      // console.log(222222222222, mapObj);
    } catch (error) {
      console.warn('无法获取时间配置，跳过时间处理:', error);
    }
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  },
  onShareAppMessage: function () {
    return {
      title: '邀请你使用企业猎搜，300+筛选维度供你选择', //自定义转发标题
      path: getShareUrl('/companyPackage/pages/searTerm/sear-term'), //分享页面路径
      imageUrl:
        'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/aa2.png' //图片后面换
    };
  }
});
