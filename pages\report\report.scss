.pages {
  width: 100%;
  min-height: 100vh;
  background: #f7f7f7;
}

// 头部区域
.h_head {
  position: relative;
  z-index: 12;
  width: 100%;
  height: 312rpx;
  image {
    width: 100%;
    height: 100%;
  }

  .bg_img {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
}

// 搜索区域
.search_wrap {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 24rpx;
  padding: 0 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .h_head_input {
    display: flex;
    align-items: center;
    width: 100%;
    height: 72rpx;
    background: #ffffff;
    border-radius: 8rpx;
    box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(7, 110, 228, 0.1);
    padding: 0 0 0 28rpx;
    box-sizing: border-box;
    font-size: 28rpx;
    font-family:
      PingFang SC,
      PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #9b9eac;

    .h_search {
      min-width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
    }

    .texts {
      font-weight: 400;
      font-size: 28rpx;
      color: #9b9eac;
      width: 100%;
    }
  }
}
// 缺省页面样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx;
  background: #fff;
  min-height: 400rpx;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 24rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #9b9eac;
    font-weight: 400;
  }
}

// 标题
.industry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx;
  height: 92rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;

  .industry-title {
    font-weight: 600;
    font-size: 32rpx;
    color: #20263a;

    .txt {
      font-weight: 400;
      font-size: 24rpx;
      color: #9b9eac;
      margin-left: 12rpx;
    }
  }

  .industry-more {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #e72410;

    .arrow-icon {
      width: 20rpx;
      height: 20rpx;
      margin-left: 8rpx;
    }
  }
}
.zt-container {
  padding: 20rpx 24rpx;
  background: #fff;

  .zt-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); // 一行4个
    grid-template-rows: repeat(2, 1fr); // 一共2行
    gap: 16rpx 20rpx;

    .zt-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .zt-image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-bottom: 8rpx;
        overflow: hidden;
      }

      .zt-name {
        font-weight: 400;
        font-size: 24rpx;
        color: #20263a;
        line-height: 32rpx;
        text-align: center;
      }
    }
  }
}
.ai_img {
  width: 750rpx;
  height: 172rpx;
  margin: 20rpx 0;
}
// 卡片
.list {
  // 确保列表在scroll-view中正常显示
  background: #ffffff;

  .item {
    display: flex;
    background: #ffffff;
    padding: 32rpx 24rpx;
    position: relative;
    // 优化触摸反馈
    transition: background-color 0.2s ease;

    // 1px底部边框
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 24rpx;
      width: 702rpx;
      height: 1rpx;
      background: #eee;
      transform: scaleY(0.5);
    }

    &:last-child::after {
      display: none;
    }

    // 点击态效果（适配scroll-view）
    &:active {
      background-color: #f8f9fa;
    }

    .image-container {
      flex-shrink: 0;
      margin-right: 20rpx;
      position: relative;

      .report-image {
        width: 104rpx;
        height: 136rpx;
      }

      .txt {
        position: absolute;
        width: 104rpx;
        height: 28rpx;
        background: linear-gradient(270deg, #fd9331 0%, #ffb93e 100%);
        font-weight: 600;
        font-size: 20rpx;
        color: #ffffff;
        top: 84rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;

      .title {
        font-weight: 400;
        font-size: 28rpx;
        color: #20263a;
        margin-bottom: 16rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
      }

      .info-row {
        margin-bottom: 16rpx;
        // 整体最多2行，超出截取
        max-height: calc(36rpx * 2 + 8rpx); // 2行的高度 + 间距
        overflow: hidden;

        // 使用flex布局，允许换行
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx 16rpx;
        align-items: center;

        .file-info {
          font-weight: 400;
          font-size: 24rpx;
          color: #9b9eac;
          border-radius: 4rpx;
          border: 1rpx solid #a0a5ba;
          padding: 0 10rpx;
          height: 36rpx;
          display: flex;
          align-items: center;
          flex-shrink: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200rpx;
        }

        .tags {
          display: contents; // 让tags的子元素直接参与父级的flex布局

          .tag {
            height: 36rpx;
            padding: 0 10rpx;
            background: rgba(74, 184, 255, 0.1);
            border-radius: 4rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #4ab8ff;
            max-width: 200rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: flex;
            align-items: center;
            flex-shrink: 0;

            &:nth-of-type(2) {
              background: rgba(253, 147, 49, 0.1);
              color: #fd9331;
            }
          }
        }
      }

      .bottom-row {
        display: flex;
        align-items: center;

        .organization {
          font-weight: 400;
          font-size: 24rpx;
          color: #74798c;
          margin-right: 40rpx;
          // 最多2行
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .date {
          font-weight: 400;
          font-size: 24rpx;
          color: #9b9eac;
          flex-shrink: 0;
        }
      }
    }
  }

  // hover效果类
  .item-hover {
    background-color: #f8f9fa !important;
  }
  .item:last-child {
    margin-bottom: 24rpx;
  }
}
.blue {
  border: 1px solid blue;
}

// 研报列表相关样式
.report-scroll-view {
  min-height: 200rpx; /* 一个卡片的高度 */
  max-height: 82vh;
}

.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  margin: 20rpx 0;
  background: #fff;
  border-radius: 8rpx;
  color: #e51603;
  font-size: 28rpx;
  font-weight: 500;

  &:active {
    background: #f0f0f0;
  }
}

.loading-status,
.no-more-status {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  margin: 20rpx 0;
  color: #999;
  font-size: 26rpx;
}
