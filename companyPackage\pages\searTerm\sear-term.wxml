<import src="/template/null/null"></import>
<view class="searpages">
  <!-- 搜索模版 -->
  <ConfigurableForm
    bindsubmit="getParams"
    id="sear-hunt"
    isPage
    bindvip="vipPop"
  />
  <!--  -->
  <view class="footer" style="height: {{isIphoneX?'168rpx':'110rpx'}};">
    <text bindtap="clearSear">清空条件</text>
    <text bindtap="saveSear">保存模板</text>
    <text bindtap="submitSear">立即搜索</text>
  </view>
  <!-- 保存选项弹窗 -->
  <view class="fadeIn mask" wx:if="{{saveSearPop}}">
    <view class="box">
      <view class="tit">保存选项</view>
      <view class="inpt">
        <input
          type="text"
          model:value="{{saveSearVal}}"
          placeholder="请输入自定义保存模版名称"
          placeholder-class="placeholder-input"
        />
      </view>
      <view class="btn">
        <view bindtap="saveSearCancel">取消</view>
        <!-- 确定判断是否为空 吐司提示 -->
        <view style="color:#E72410;" bindtap="saveSearSub">确定</view>
      </view>
    </view>
  </view>
  <!-- 搜索模版 -->
  <half-screen-pop
    title="搜索模版"
    disableAnimation="{{true}}"
    position="bottom"
    visible="{{searPop}}"
    zIndex="{{100}}"
    confirmBtnText="应用"
    cancelBtnText="退出"
    bindsubmit="searsub"
  >
    <view class="sear-con" slot="customContent">
      <block wx:if="{{templateAry.length>0}}">
        <view class="sear-con-h">
          <scroll-view class="sear-con-h-box" scroll-y>
            <block wx:for="{{templateAry}}" wx:key="index">
              <view
                class="item {{item.active&&'active'}}"
                data-item="{{item}}"
                bindtap="onsearItm"
                data-index="{{index}}"
              >
                <text class="item-l">{{item.title}}</text>
                <image
                  class="item-r"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/del.png"
                  wx:if="{{true}}"
                  catchtap="onsearDel"
                  data-item="{{item}}"
                  data-index="{{index}}"
                ></image>
                <image
                  class="item-r"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/del_a.png"
                  wx:if="{{false}}"
                  catchtap="onsearDel"
                  data-item="{{item}}"
                  data-index="{{index}}"
                ></image>
              </view>
            </block>
          </scroll-view>
        </view>
        <view class="sear-con-b">
          <view class="title">企业规模</view>
          <scroll-view scroll-y class="tagbox">
            <view class="wrap">
              <block wx:for="{{renderAry}}" wx:key="index">
                <view class="tag">{{item.name+':'+item.value}} </view>
              </block>
            </view>
          </scroll-view>
        </view>
      </block>
      <block wx:else>
        <view style="height: 550rpx;">
          <template is="null"></template>
        </view>
      </block>
    </view>
  </half-screen-pop>
  <!-- 搜索按钮 -->
  <view class="s-btn" bindtap="onsearPop">
    <image
      src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search-white.png"
      mode="aspectFit"
    ></image>
    <view class="txt">搜模板</view>
  </view>
  <!-- vip弹窗 -->
  <VipPop visible="{{vipVisible}}"></VipPop>
</view>
