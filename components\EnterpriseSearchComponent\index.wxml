<view class="enterprise-search-component">
  <!-- 筛选条件 -->
  <DropDownMenu
    height="{{filtrateHeight}}"
    dropDownMenuTitle="{{dropDownMenuTitle}}"
    class="drop-menu"
    bindsubmit="onFlitter"
    heightParams="{{heightParams}}"
    excludeFields="{{excludeFields}}"
    useIndustrialList="{{true}}"
    bindvip="vipPop"
  />

  <!-- 企业列表 -->
  <view class="enterprise-list">
    <view class="tip" wx:if="{{enterpriseList.length > 0}}">
      共找到<text class="count">{{totalCount}}</text
      >家企业
    </view>

    <scroll-view
      scroll-y
      style="height: {{computedHeight}}px;"
      bindscrolltolower="onScrollToLower"
      bindscroll="onScroll"
      refresher-enabled
      refresher-triggered="{{isLoading}}"
      bindrefresherrefresh="onRefresh"
    >
      <view class="card-list">
        <block wx:if="{{enterpriseList.length > 0}}">
          <ComCard
            wx:for="{{enterpriseList}}"
            wx:key="index"
            obj="{{item}}"
            btn="collect"
            bindcardFun="onCardAction"
            bindhandleTit="handleTit"
            isCheckVip="{{isCheckVip}}"
          />
        </block>

        <!-- 加载状态 -->
        <view
          wx:if="{{isLoading && enterpriseList.length > 0}}"
          class="loading-more"
        >
          <text>加载中...</text>
        </view>

        <!-- 没有更多数据 -->
        <view
          wx:if="{{!hasMoreData && enterpriseList.length > 0}}"
          class="no-more"
        >
          <text>没有更多数据了</text>
        </view>

        <!-- VIP限制 -->
        <view
          wx:if="{{!isLogin && enterpriseList.length >= 5}}"
          class="vip-limit"
        >
          <vloginOverlay bindsubmit="login"></vloginOverlay>
        </view>

        <view
          wx:if="{{permission === '普通VIP' && enterpriseList.length >= 20}}"
          class="vip-limit"
        >
          <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
        </view>
      </view>
    </scroll-view>

    <!-- 暂无数据 -->
    <view wx:if="{{isNull}}" class="empty-state">
      <text>暂无企业数据</text>
    </view>
  </view>
</view>

<!-- 联系方式弹窗 -->
<Contact visible="{{showContact}}" entId="{{activeEntId}}"></Contact>

<!-- 地址弹窗 -->
<dialog
  visible="{{showAddress}}"
  title="地址"
  isShowConfirm="{{false}}"
  showFooter="{{false}}"
>
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map
        id="map"
        longitude="{{location.lon}}"
        latitude="{{location.lat}}"
        markers="{{addmarkers}}"
        scale="{{11}}"
        style="width: 100%; height: 306rpx;"
      >
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <view bindtap="goMap" class="map"> 导航 </view>
    <view class="cancel" bindtap="onCloseAddress"> 取消 </view>
  </view>
</dialog>

<!-- VIP弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>
