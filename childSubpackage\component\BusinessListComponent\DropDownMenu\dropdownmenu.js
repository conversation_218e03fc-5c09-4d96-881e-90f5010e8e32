import {dataHelpers} from '../../../../components/ConfigurableForm/utils/helpers';
import {
  clearChildComponent,
  fillChildComponent,
  getChildComponentHasVal,
  handleData
} from '../../../../components/ConfigurableForm/utils/component-helpers';
var behavior = require('../../../../template/menuhead/index');

const app = getApp();
Component({
  behaviors: [behavior],
  properties: {
    // 下拉菜单配置，用于动态决定每个位置显示什么功能
    dropDownMenuConfig: {
      type: Array,
      value: ['region', 'industry', 'filter'], // 默认配置：地区、产业链、更多筛选
      observer() {
        this.updateMenuConfig();
      }
    },

    // 外部筛选状态（用于回显）
    externalFilterState: {
      type: Object,
      value: null,
      observer(newVal) {
        // 只有在组件初始化完成后才执行
        if (
          this.data.isInitialized &&
          Object.keys(newVal)?.length &&
          !this.data.isBackfilling
        ) {
          this.applyExternalState(newVal);
        }
      }
    },

    // 是否使用产业链专用选择器
    useIndustrialList: {
      type: Boolean,
      value: false
    },
    // 产业链代码（用于IndustrySingleSelect获取数据）
    chain_code: {
      type: String,
      value: ''
    },
    // 是否购买拦截 -默认购买不拦截 以后也可以作为点击拦截
    purchased: {
      type: Boolean,
      value: true
    }
  },
  data: {
    isIphoneX: app.globalData.isIphoneX,
    // 筛选相关
    regionData: {}, //选中的地区
    chainData: {}, //选中的产业链
    moreFilterParams: {}, // 更多筛选参数
    temParams: {}, //更多筛选改变的时候赋值
    allChainData: [],
    selectChainVal: '',
    isBackfilling: false, // 是否正在回填数据的标志

    // 动态菜单配置
    menuConfig: {
      region: {position: 0, enabled: true},
      industry: {position: 1, enabled: true},
      filter: {position: 2, enabled: true}
    },
    isInitialized: false // 添加初始化标志
  },

  observers: {
    regionData: function (obj) {
      if (obj?.code && obj.code != 'All') {
        this.setData({
          district_val: true
        });
      }
    },
    chainData: function (obj) {
      if (obj.code) {
        this.setData({
          source_val: true
        });
      }
    },
    filter_open: function (val) {
      if (!val) return;
      // 回填更多筛选
      clearChildComponent(this, '#s-hunt')();

      // 延迟回填，确保 ConfigurableForm 组件已经渲染完成
      setTimeout(() => {
        const moreFilterParams = this.data.moreFilterParams;
        if (moreFilterParams && Object.keys(moreFilterParams).length > 0) {
          fillChildComponent(
            this,
            '#s-hunt'
          )(dataHelpers.deepClone(moreFilterParams));
        }
      }, 100);
    }
  },

  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      this.updateMenuConfig();
      this.setData({isInitialized: true});

      // 检查是否有外部筛选状态需要应用
      if (
        this.properties.externalFilterState &&
        Object.keys(this.properties.externalFilterState).length
      ) {
        this.applyExternalState(this.properties.externalFilterState);
      }
    }
  },

  methods: {
    // 更新菜单配置
    updateMenuConfig() {
      const {dropDownMenuConfig} = this.properties;

      const menuConfig = {
        region: {position: -1, enabled: false},
        industry: {position: -1, enabled: false},
        filter: {position: -1, enabled: false}
      };

      // 根据配置数组设置每个功能的位置
      dropDownMenuConfig.forEach((type, index) => {
        if (menuConfig[type]) {
          menuConfig[type] = {position: index, enabled: true};
        }
      });

      this.setData({menuConfig});
    },

    // 地区
    getRegion(e) {
      let {selection} = e.detail;
      let {regionData, district_val} = this.data;

      if (selection?.code == 'All') {
        regionData = {};
        district_val = false;
      } else {
        regionData = selection;
        district_val = true;
      }
      this.setData(
        {
          regionData,
          district_val
        },
        () => this.backAll()
      );
    },

    // 选中产业链
    handleSelectChain({detail}) {
      const {selection} = detail;
      this.setData(
        {
          chainData: selection,
          selectChainVal: selection.code
        },
        () => this.backAll()
      );
      this.closeHyFilter();
    },

    closeRegion() {
      this.closeHyFilter();
    },

    // ConfigurableForm改变
    onConfigurableFormSubmit(e) {
      const {paramsData} = e.detail;
      const clonedData = dataHelpers.deepClone(paramsData);
      this.setData({
        temParams: clonedData
      });
    },

    // ConfigurableForm VIP事件
    onConfigurableFormVip(e) {
      this.triggerEvent('vip', e.detail);
    },

    // ConfigurableForm 初始化完成
    onConfigurableFormReady() {
      // 如果有待回填的数据，现在进行回填
      if (
        this.data.moreFilterParams &&
        Object.keys(this.data.moreFilterParams).length > 0
      ) {
        const configurableForm = this.selectComponent('#s-hunt');
        if (configurableForm && configurableForm.setBackfillData) {
          configurableForm.setBackfillData(this.data.moreFilterParams);
        }
      }
    },

    // 确定更多筛选
    confirmMoreFilter() {
      const {temParams} = this.data;
      const clonedParams = dataHelpers.deepClone(temParams);
      this.setData(
        {
          moreFilterParams: clonedParams
        },
        () => {
          this.backAll(true);
        }
      );
    },

    resetMoreFilter() {
      clearChildComponent(this, '#s-hunt')();
      this.setData({
        temParams: {}
      });
    },

    // 总的返回 - 支持更多筛选参数
    backAll(type = false) {
      const {regionData, chainData, moreFilterParams} = this.data;

      const requestData = {
        name1: regionData?.name || '全国',
        name2: chainData?.name || '产业类型',
        // 修复：area_code_list 应该是数组格式
        area_code_list:
          regionData?.code && regionData.code !== 'All'
            ? [regionData.code]
            : [],
        industrial_list: chainData?.code ? [chainData.code] : [],
        ...handleData(moreFilterParams),
        isFilter: type
      };

      console.log('DropDownMenu 返回数据:', requestData);
      this.triggerEvent('submit', requestData);
      this.closeHyFilter();
    },

    // 应用外部状态
    applyExternalState(externalState) {
      if (!externalState) return;

      this.setData({isBackfilling: true});

      const {regionData, industrial_list, filterParams} = externalState;

      // 处理地区数据
      if (regionData) {
        this.setData({regionData});
      }

      // 处理产业链数据
      if (industrial_list) {
        this.setData({
          chainData: industrial_list,
          selectChainVal: industrial_list.code
        });
      }

      // 处理更多筛选参数
      if (filterParams) {
        this.setData({moreFilterParams: filterParams});

        // 调用 ConfigurableForm 的回填方法
        const configurableForm = this.selectComponent('#s-hunt');
        if (configurableForm && configurableForm.setBackfillData) {
          configurableForm.setBackfillData(filterParams);
        }
      }

      this.setData({isBackfilling: false});
    }
  }
});
