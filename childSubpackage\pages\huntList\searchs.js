import {commonRequest} from '../../../utils/mixin/loginbeforeReq';
const app = getApp();
Page({
  data: {
    // BusinessListComponent 相关
    externalFilterState: {},
    chain_code: '',
    chain_name: '',
    category: ''
  },
  onShow() {
    // 初始化公共数据（地区、行业等）
    commonRequest();
  },
  onLoad(options) {
    let heightParams = options?.str;

    // 说明是从高级搜索那边过来的
    if (heightParams) {
      heightParams = JSON.parse(decodeURIComponent(heightParams));

      // 转换为 BusinessListComponent 需要的格式
      const externalFilterState =
        this.convertToExternalFilterState(heightParams);

      // 设置产业链相关数据
      let chain_code = '';
      let chain_name = '';
      let category = 'hot'; // 默认设为热点产业

      if (heightParams.industrial_list?.length > 0) {
        const firstIndustrial = heightParams.industrial_list[0];
        chain_code = firstIndustrial.code || '';
        chain_name = firstIndustrial.name || '';
      }

      this.setData({
        externalFilterState,
        chain_code,
        chain_name,
        category
      });
    }
  },

  /**
   * 将高级搜索的参数转换为 BusinessListComponent 需要的 externalFilterState 格式
   */
  convertToExternalFilterState(heightParams) {
    const externalFilterState = {
      filterParams: {}
    };

    // 处理地区数据
    if (heightParams.area_code_list?.length > 0) {
      externalFilterState.regionData = heightParams.area_code_list[0];
    }

    // 处理产业链数据
    if (heightParams.industrial_list?.length > 0) {
      const item = heightParams.industrial_list[0];
      if (item.tabCode === 'classic') {
        externalFilterState.classic_industry_code_list = item;
      } else {
        externalFilterState.industrial_list = item;
      }
    }

    // 处理其他筛选条件（企业名称、行业等）
    const filterParams = {};

    // 企业名称
    if (heightParams.ent_name) {
      filterParams.ent_name = heightParams.ent_name;
    }

    // 行业
    if (heightParams.industry_code_list?.length > 0) {
      filterParams.industry_code_list = heightParams.industry_code_list;
    }

    // 其他字段
    Object.keys(heightParams).forEach(key => {
      if (
        ![
          'area_code_list',
          'industrial_list',
          'ent_name',
          'industry_code_list'
        ].includes(key)
      ) {
        filterParams[key] = heightParams[key];
      }
    });

    externalFilterState.filterParams = filterParams;

    return externalFilterState;
  }
});
