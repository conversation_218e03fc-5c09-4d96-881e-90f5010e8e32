<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<view class="pages">
  <view>
    <!-- 使用 BusinessListComponent -->
    <BusinessListComponent
      externalFilterState="{{externalFilterState}}"
      useIndustrySingleSelect="{{true}}"
      chain_code="{{chain_code}}"
      chain_name="{{chain_name}}"
      category="{{category}}"
      showMapMode="{{showMapMode}}"
      fixedTitle="{{pageTitle}}"
    />
  </view>
</view>

<!-- 联系方式弹窗 -->
<Contact visible="{{showContact}}" entId="{{activeEntId}}"></Contact>

<!-- 地址弹窗 -->
<dialog
  visible="{{showAddress}}"
  title="地址"
  isShowConfirm="{{false}}"
  showFooter="{{false}}"
>
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map
        id="map"
        longitude="{{location.lon}}"
        latitude="{{location.lat}}"
        markers="{{addmarkers}}"
        scale="{{11}}"
        style="width: 100%; height: 306rpx;"
      >
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <view bindtap="goMap" class="map"> 导航 </view>
    <view class="cancel" bindtap="onCloseAddress"> 取消 </view>
  </view>
</dialog>
<!-- vip弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>
