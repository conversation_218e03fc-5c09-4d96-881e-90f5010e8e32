.wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 32rpx 24rpx 32rpx;
  gap: 18rpx;
  transition: all 0.3s ease;

  .item {
    position: relative;
    width: 342rpx;
    height: 140rpx;
    background: linear-gradient(180deg, #f7f7f7 0%, #ffffff 100%);
    border-radius: 12rpx;
    border: 1rpx solid #eeeeee;
    padding: 24rpx;
    box-sizing: border-box;
    overflow: hidden;
    transition: all 0.2s ease;

    // 企业类型名称
    .item-name {
      font-weight: 400;
      font-size: 28rpx;
      color: #525665;
      word-break: break-all;
      margin-bottom: 16rpx;
    }

    // 数量信息
    .item-count {
      font-weight: 600;
      font-size: 32rpx;
      color: #20263a;

      .item-unit {
        font-weight: 400;
        font-size: 24rpx;
        color: #9b9eac;
        margin-left: 4rpx;
      }
    }

    // 背景装饰图片
    .item-bg {
      position: absolute;
      bottom: 16rpx;
      right: 16rpx;
      width: 56rpx;
      height: 56rpx;
      z-index: 1;
    }
  }

  // hover效果类
  .item-active {
    transform: scale(0.98) !important;
    opacity: 0.9 !important;
  }

  // 如果是奇数个，最后一个居左
  .item:nth-child(odd):last-child {
    margin-right: auto;
  }

  // 加载动画
  .item {
    animation: fadeInUp 0.6s ease forwards;

    // 初始状态
    &:not(.active) {
      opacity: 0;
      transform: translateY(20rpx);
    }

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{($i - 1) * 0.1}s;
      }
    }
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
