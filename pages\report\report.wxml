<view class="chain_index">
  <immersive-navbar
    id="navbar"
    title="报告"
    show-back-btn="{{false}}"
    immersive="{{true}}"
    bg-color="#e51603"
    text-color="#fff"
  >
    <view slot="content" class="pages">
      <view class="h_head">
        <image class="bg_img" src="/image/report/r_headbg.png" />
        <view class="search_wrap">
          <view
            hover-class="none"
            class="h_head_input"
            bindtap="handleClick"
            data-type="search"
          >
            <view class="h_search">
              <image
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
              ></image>
            </view>
            <view class="texts text-ellipsis">请输入机构/研报关键字</view>
          </view>
        </view>
      </view>
      <!-- 主要内容区域 -->
      <view class="main-content">
        <!-- 产业专题 -->
        <block>
          <view class="industry-header">
            <view class="industry-title">产业专题</view>
            <view class="industry-more" data-type="zt" bindtap="onMoreClick">
              更多
              <image class="arrow-icon" src="/image/report/r_arrow_red.png" />
            </view>
          </view>
          <view class="zt-container">
            <view class="zt-grid">
              <view
                wx:for="{{ztData}}"
                wx:key="index"
                class="zt-item"
                bindtap="onZtClick"
                data-item="{{item}}"
              >
                <image
                  class="zt-image"
                  src="{{item.img || '/placeholder-image.png'}}"
                  mode="aspectFill"
                />
                <view class="zt-name">{{item.name}}</view>
              </view>
            </view>
          </view>
        </block>
        <!-- ai图片占位  -->
        <image
          class="ai_img"
          src="/image/report/r_ai_enter.png"
          bindtap="handleClick"
          data-type="ai"
        />

        <!-- 研报图表 -->
        <block>
          <view class="industry-header">
            <view class="industry-title"
              >研报图表<text class="txt"
                >研报图表A提取，无需阅读即可掌握精华
              </text></view
            >
            <view class="industry-more" data-type="tb" bindtap="onMoreClick">
              更多
              <image class="arrow-icon" src="/image/report/r_arrow_red.png" />
            </view>
          </view>
          <view class="zt-container" style="padding: 40rpx;">
            <view class="zt-grid" style="gap: 62rpx 32rpx;">
              <view
                wx:for="{{tbData}}"
                wx:key="index"
                class="zt-item"
                bindtap="onTbClick"
                data-item="{{item}}"
              >
                <image
                  class="zt-image"
                  src="/image/report/{{item.code}}.png"
                  mode="aspectFill"
                  style="margin-bottom: 16rpx;"
                />
                <view class="zt-name">{{item.name}}</view>
              </view>
            </view>
          </view>
        </block>
        <!-- 撼地智库 -->
        <block>
          <view class="industry-header">
            <view class="industry-title">撼地智库</view>
            <view class="industry-more" data-type="zk" bindtap="onMoreClick">
              更多
              <image class="arrow-icon" src="/image/report/r_arrow_red.png" />
            </view>
          </view>

          <!-- 已登录且有数据时显示列表 -->
          <scroll-view
            wx:if="{{isLogin && reportList.length > 0}}"
            class="report-scroll-view"
            scroll-y="{{true}}"
            refresher-enabled="{{isVip}}"
            refresher-triggered="{{reportLoading}}"
            bindrefresherrefresh="onPullDownRefresh"
            enhanced="{{true}}"
            bounces="{{false}}"
          >
            <view class="list">
              <view
                class="item"
                wx:for="{{reportList}}"
                wx:key="index"
                bindtap="onItemClick"
                data-index="{{index}}"
                hover-class="item-hover"
                hover-stay-time="150"
                hover-start-time="0"
              >
                <!-- 左边图片 -->
                <view class="image-container">
                  <image
                    class="report-image"
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_yb_ico.png"
                    mode="aspectFill"
                  ></image>
                  <!-- 图片上面文字 -->
                  <view class="txt">{{item.imgTit || '产业研究'}}</view>
                </view>

                <!-- 右边内容 -->
                <view class="content">
                  <!-- 标题 -->
                  <view class="title">{{item.title}}</view>

                  <!-- 信息行：页数、大小、标签 -->
                  <view class="info-row">
                    <view class="file-info"
                      >{{item.page_num}}页 {{item.size}}</view
                    >
                    <view class="tags">
                      <view
                        class="tag"
                        wx:for="{{item.tags}}"
                        wx:key="tagIndex"
                        wx:for-item="tag"
                      >
                        {{tag}}
                      </view>
                    </view>
                  </view>

                  <!-- 底部：机构和时间 -->
                  <view class="bottom-row">
                    <view class="organization">{{item.organization}}</view>
                    <view class="date">{{item.date}}</view>
                  </view>
                </view>
              </view>

              <!-- 查看更多按钮 -->
              <view
                wx:if="{{reportHasMore && !reportLoading && isVip}}"
                class="load-more-btn"
                bindtap="onLoadMore"
              >
                查看更多
              </view>

              <!-- 加载中状态 -->
              <view wx:if="{{reportLoading}}" class="loading-status">
                加载中...
              </view>

              <!-- 没有更多数据 -->
              <view
                wx:if="{{!reportHasMore && reportList.length > 0 && isVip}}"
                class="no-more-status"
              >
                已加载全部内容
              </view>
            </view>
          </scroll-view>

          <!-- 缺省页面：未登录或无数据时显示 -->
          <view
            wx:if="{{!isLogin || reportList.length === 0}}"
            class="empty-state"
            bindtap="handleEmptyClick"
          >
            <image
              class="empty-image"
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png"
              mode="aspectFit"
            />
            <view class="empty-text">暂无数据</view>
          </view>

          <!-- VIP页面组件 - 非VIP用户显示 -->
          <vip-page wx:if="{{showVipPage}}" bindpaySuccess="onVipPaySuccess">
            <!-- 可以在这里添加额外的内容 -->
          </vip-page>
        </block>
      </view>
    </view>
  </immersive-navbar>
</view>
