import {commonRequest} from '../../../utils/mixin/loginbeforeReq';
const app = getApp();

Page({
  data: {
    // BusinessListComponent 相关
    externalFilterState: {},
    chain_code: '',
    chain_name: '',
    category: '',
    pageTitle: '企业名单',
    showMapMode: true,
    // 弹窗相关
    showContact: false,
    activeEntId: '',
    showAddress: false,
    location: {},
    addmarkers: [],
    locationTxt: '',
    vipVisible: false
  },

  onShow() {
    // 初始化公共数据（地区、行业等）
    commonRequest();
  },

  onLoad: function (options) {
    let {
      chain_name = '',
      chain_code = '',
      isIndustryMap,
      purchased,
      category,
      title,
      showMapMode,
      region_code = '',
      region_name = '',
      filter_code = '',
      filter_name = '',
      str // 高级搜索参数
    } = options;

    // 处理高级搜索参数
    if (str) {
      const heightParams = JSON.parse(decodeURIComponent(str));
      const externalFilterState =
        this.convertToExternalFilterState(heightParams);

      // 设置产业链相关数据
      let searchChainCode = '';
      let searchChainName = '';
      let searchCategory = 'hot'; // 默认设为热点产业

      if (heightParams.industrial_list?.length > 0) {
        const firstIndustrial = heightParams.industrial_list[0];
        searchChainCode = firstIndustrial.code || '';
        searchChainName = firstIndustrial.name || '';
      }

      this.setData({
        externalFilterState,
        chain_code: searchChainCode,
        chain_name: searchChainName,
        category: searchCategory,
        pageTitle: title ? decodeURIComponent(title) : '企业搜索',
        showMapMode: showMapMode === 'false' ? false : true
      });

      // 动态设置导航名
      wx.setNavigationBarTitle({
        title: title ? decodeURIComponent(title) : '企业搜索'
      });
      return;
    }

    // 处理普通参数
    region_name = region_name ? decodeURIComponent(region_name) : '';
    filter_name = filter_name ? decodeURIComponent(filter_name) : '';
    chain_name = chain_name ? decodeURIComponent(chain_name) : '产业类型';
    title = title ? decodeURIComponent(title) : '企业名单';
    purchased = purchased !== 'false';
    showMapMode = showMapMode === 'false' ? false : true;

    let tempExternalFilterState = {
      filterParams: {}
    };

    if (region_code) {
      tempExternalFilterState['regionData'] = {
        code: region_code,
        name: region_name
      };
    }

    // 处理产业链参数
    if (filter_code || chain_code) {
      const finalChainCode = filter_code || chain_code;
      const finalChainName = filter_name || chain_name;

      // 经典字段不一样
      if (category === 'classic') {
        tempExternalFilterState['classic_industry_code_list'] = {
          code: finalChainCode,
          name: finalChainName
        };
      } else if (category === 'hot') {
        // 热点和产业链图谱
        tempExternalFilterState['industrial_list'] = {
          code: finalChainCode,
          name: finalChainName
        };
      } else {
        // 产业链图谱 category 为chainMap
        tempExternalFilterState['industrial_list'] = {
          code: finalChainCode,
          name: finalChainName
        };
      }
    }

    // 处理特殊企业类型
    if (title === '上市企业') {
      tempExternalFilterState['filterParams'] = {
        listing_status: ['A', 'B', 'NTB', 'HK', 'STAR', 'USA']
      };
    } else if (title === '高新技术企业') {
      tempExternalFilterState['filterParams'] = {
        technology: ['HN']
      };
    } else if (title === '专精特新企业') {
      tempExternalFilterState['filterParams'] = {
        psn: ['SME', 'CMI', 'SG', 'IME']
      };
    } else if (title === '科技型企业') {
      tempExternalFilterState['filterParams'] = {
        technology: ['HN', 'MST', 'G', 'U', 'GT']
      };
    } else if (title === '小巨人') {
      tempExternalFilterState['filterParams'] = {
        psn: ['SG']
      };
    } else if (title === '单项冠军') {
      tempExternalFilterState['filterParams'] = {
        psn: ['CMI']
      };
    } else if (title === '专精特新中小企业') {
      tempExternalFilterState['filterParams'] = {
        psn: ['SME']
      };
    } else if (title === '创新型中小企业') {
      tempExternalFilterState['filterParams'] = {
        psn: ['IME']
      };
    }

    tempExternalFilterState.filterParams = {
      ...tempExternalFilterState.filterParams,
      ent_entity_type: ['1']
    };

    this.setData({
      externalFilterState: tempExternalFilterState,
      chain_code: filter_code || chain_code,
      chain_name: filter_name || chain_name,
      category,
      pageTitle: title,
      showMapMode
    });

    // 动态设置导航名
    wx.setNavigationBarTitle({
      title
    });
  },

  /**
   * 将高级搜索的参数转换为 BusinessListComponent 需要的 externalFilterState 格式
   */
  convertToExternalFilterState(heightParams) {
    const externalFilterState = {
      filterParams: {}
    };

    // 处理地区数据
    if (heightParams.area_code_list?.length > 0) {
      const firstArea = heightParams.area_code_list[0];
      externalFilterState.regionData = {
        code: firstArea.code || firstArea,
        name: firstArea.name || firstArea
      };
    }

    // 处理产业链数据
    if (heightParams.industrial_list?.length > 0) {
      const firstIndustrial = heightParams.industrial_list[0];
      externalFilterState.industrial_list = {
        code: firstIndustrial.code || firstIndustrial,
        name: firstIndustrial.name || firstIndustrial
      };
    }

    // 处理其他筛选参数
    const filterParams = {...heightParams};
    delete filterParams.area_code_list;
    delete filterParams.industrial_list;

    externalFilterState.filterParams = filterParams;

    return externalFilterState;
  }
});
