import {dataHelpers} from '../ConfigurableForm/utils/helpers';
import {
  clearChildComponent,
  fillChildComponent,
  getChildComponentHasVal,
  getNameFromPop,
  handleMultiple,
  handleData
} from '../ConfigurableForm/utils/component-helpers';
import {getHeight} from '../../utils/height';

var behavior = require('../../template/menuhead/index');
const app = getApp();
Component({
  behaviors: [behavior],
  properties: {
    // 筛选参数（用于回填数据）
    heightParams: {
      type: Object,
      observer(val) {
        if (!Object.keys(val || {}).length) return;
        let {
          industry_code_list = [],
          industrial_list = [],
          area_code_list = [],
          ...rest
        } = dataHelpers.deepClone(val);
        // 处理地区、行业和产业链数据的回填
        this.setData({
          area_code_list: area_code_list,
          industry_code_list: industry_code_list,
          industrial_list: industrial_list,
          moreFilterParams: dataHelpers.deepClone(rest || {})
        });
      }
    },
    // 下拉菜单标题
    dropDownMenuTitle: {
      type: Array,
      value: []
    },
    // 排除的筛选字段
    excludeFields: {
      type: Array,
      value: ['area_code_list', 'industry_code_list'] // 默认排除地区和行业，因为单独处理
    },
    // 是否使用产业链选择（而不是行业选择）
    useIndustrialList: {
      type: Boolean,
      value: false
    }
  },
  data: {
    isIphoneX: app.globalData.isIphoneX,
    // 筛选相关
    area_code_list: [], //选中的地区
    industry_code_list: [],
    industrial_list: [], //选中的产业链
    selectChainVal: '', //当前选中的产业链代码
    industrialOptions: [], //产业链选项列表
    moreFilterParams: {}, // 更多筛选参数
    temParams: {} //更多筛选改变的时候赋值
  },
  observers: {
    area_code_list: function (list) {
      if (list.length > 0) {
        this.setData({
          district_val: true
        });
      }
    },
    industry_code_list: function (list) {
      if (list.length > 0) {
        this.setData({
          source_val: true
        });
      }
    },
    industrial_list: function (list) {
      if (list.length > 0) {
        this.setData({
          source_val: true,
          selectChainVal: list[0]?.code || ''
        });
      }
    },
    moreFilterParams: function (source) {
      this.setData({
        filter_val: getChildComponentHasVal(this, '#s-hunt')(source)
      });
    },
    filter_open: function (val) {
      if (!val) return;
      // 回填更多筛选
      clearChildComponent(this, '#s-hunt')();
      fillChildComponent(
        this,
        '#s-hunt'
      )(dataHelpers.deepClone(this.data.moreFilterParams));
    }
  },
  ready() {
    // 如果使用产业链模式，初始化产业链数据
    if (this.data.useIndustrialList) {
      this.initIndustrialOptions();
    }
  },
  methods: {
    // 初始化产业链选项
    initIndustrialOptions() {
      // 简化的产业链选项，实际项目中应该从API获取
      const industrialOptions = [
        {code: 'biomedical', name: '生物医药产业链', selected: false},
        {code: 'newenergy', name: '新能源产业链', selected: false},
        {code: 'electronic', name: '电子信息产业链', selected: false},
        {code: 'automobile', name: '汽车产业链', selected: false},
        {code: 'equipment', name: '高端装备制造产业链', selected: false},
        {code: 'material', name: '新材料产业链', selected: false}
      ];

      this.setData({industrialOptions});
    },

    // 选择产业链项目
    selectIndustrial(e) {
      const {item} = e.currentTarget.dataset;
      let {industrialOptions} = this.data;

      // 切换选中状态
      industrialOptions = industrialOptions.map(option => {
        if (option.code === item.code) {
          return {...option, selected: !option.selected};
        }
        return option;
      });

      this.setData({industrialOptions});
    },

    // 确认产业链选择
    confirmIndustrial() {
      const {industrialOptions} = this.data;
      const selectedOptions = industrialOptions.filter(item => item.selected);

      let {industrial_list, source_val} = this.data;
      industrial_list = selectedOptions;
      source_val = industrial_list.length > 0;

      this.setData(
        {
          industrial_list,
          source_val
        },
        () => this.backAll()
      );
    },

    // 地区
    getRegion(e) {
      let data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
      // console.log('获取的地区数据', data)
      let {area_code_list, district_val} = this.data;

      if (data[0]?.code == 'all') {
        //说明是全国的 就清除选中的
        area_code_list = [];
      } else {
        area_code_list = data;
      }
      district_val = area_code_list.length > 0;
      this.setData(
        {
          area_code_list,
          district_val
        },
        () => this.backAll()
      );
    },
    // 全部行业
    getIndustry(e) {
      const data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
      let {industry_code_list, source_val} = this.data;
      if (data[0]?.code == 'all') {
        //说明是全国的 就清除选中的
        industry_code_list = [];
      } else {
        industry_code_list = data;
      }
      source_val = industry_code_list.length > 0;
      this.setData(
        {
          industry_code_list,
          source_val
        },
        () => this.backAll()
      );
    },
    closeRegion() {
      this.closeHyFilter();
    },

    // ========== ConfigurableForm 相关方法 ==========

    // ConfigurableForm改变
    onConfigurableFormSubmit(e) {
      const {paramsData} = e.detail;
      this.setData({
        temParams: dataHelpers.deepClone(paramsData)
      });
    },

    // ConfigurableForm VIP事件
    onConfigurableFormVip(e) {
      // 向上传递VIP事件
      this.triggerEvent('vip', e.detail);
    },
    // 确定更多筛选
    confirmMoreFilter() {
      const {temParams} = this.data;
      this.setData(
        {
          moreFilterParams: dataHelpers.deepClone(temParams)
        },
        () => {
          this.backAll(true);
        }
      );
    },
    resetMoreFilter() {
      clearChildComponent(this, '#s-hunt')();
      this.setData({
        temParams: {}
      });
    },

    // 总的返回 - 支持更多筛选参数
    backAll(type = false) {
      const {
        area_code_list,
        industry_code_list,
        industrial_list,
        moreFilterParams,
        useIndustrialList
      } = this.data;
      let arr = [],
        arr1 = [],
        arr2 = [],
        name1 = '',
        name2 = '';

      if (area_code_list.length > 0) {
        arr = handleMultiple(area_code_list);
        name1 = getNameFromPop(arr, {
          slice: true
        });
      } else {
        name1 = '全国';
      }

      // 根据 useIndustrialList 决定使用哪个字段
      if (useIndustrialList) {
        // 使用产业链数据
        if (industrial_list.length > 0) {
          arr2 = industrial_list;
          name2 = arr2.map(item => item.name).join(',');
        } else {
          name2 = '全部产业';
        }
      } else {
        // 使用行业数据
        if (industry_code_list.length > 0) {
          arr1 = handleMultiple(industry_code_list);
          name2 = getNameFromPop(arr1, {
            slice: true
          });
        } else {
          name2 = '全部行业';
        }
      }

      const requestData = {
        name1,
        name2,
        area_code_list: arr.map(i => i.code), //地区
        ...handleData(moreFilterParams), // 合并更多筛选参数
        isFilter: type
      };

      // 根据使用的字段类型添加对应数据
      if (useIndustrialList) {
        requestData.industrial_list = arr2;
      } else {
        requestData.industry_code_list = arr1.map(i => i.code); //行业
      }

      console.log('SerDropDownMenu 公共组件返回数据:', requestData);
      this.triggerEvent('submit', requestData);
      this.closeHyFilter();
    }
  }
});
