<import src="/template/menuhead/index"></import>
<view class="head">
  <template
    is="menu-head"
    data="{{selected_source_name,dropDownMenuTitle,district_open,source_open,filter_open,selected_filter_name,selected_source_name,district_val,source_val,filter_val }}"
  ></template>
</view>

<!-- 具体内容 -->
<!-- 地区 -->
<region-selection
  zIndex="{{1}}"
  top="{{top}}"
  visible="{{district_open}}"
  top="{{top}}"
  bindsubmit="getRegion"
  bindclose="closeRegion"
  oldData="{{area_code_list}}"
/>

<!-- 行业 -->
<industry-select
  wx:if="{{!useIndustrialList}}"
  zIndex="{{1}}"
  visible="{{source_open}}"
  top="{{top}}"
  oldData="{{industry_code_list}}"
  bindsubmit="getIndustry"
  dataType="eleseicAry"
  bindclose="closeRegion"
/>

<!-- 所属产业 -->
<view
  wx:if="{{useIndustrialList && source_open}}"
  class="industrial-popup"
  style="top: {{top}}px; z-index: 1;"
>
  <view class="industrial-content">
    <view class="industrial-header">选择所属产业</view>
    <view class="industrial-list">
      <view
        class="industrial-item {{item.selected ? 'selected' : ''}}"
        wx:for="{{industrialOptions}}"
        wx:key="code"
        data-item="{{item}}"
        bindtap="selectIndustrial"
      >
        <text>{{item.name}}</text>
        <image
          wx:if="{{item.selected}}"
          class="check-icon"
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/industry/i_checked.png"
        />
      </view>
    </view>
    <view class="industrial-footer">
      <button class="btn-cancel" bindtap="closeRegion">取消</button>
      <button class="btn-confirm" bindtap="confirmIndustrial">确定</button>
    </view>
  </view>
</view>

<!-- 更多筛选 - 使用ConfigurableForm  -->
<view
  class="container container_hd  line-tesu {{filter_open ? 'show' : 'disappear'}}"
  style="height: {{computedHeight}}px;"
>
  <view
    style="height: {{computedHeight-(isIphoneX ? 85:55)}}px; overflow: hidden;"
  >
    <ConfigurableForm
      variant="full"
      excludeFields="{{excludeFields}}"
      wrapHeight="{{computedHeight-(isIphoneX ? 85:55)}}px"
      bindsubmit="onConfigurableFormSubmit"
      bindvip="onConfigurableFormVip"
      id="s-hunt"
    />
  </view>
  <!-- 底部按钮 -->
  <view class="footer" style="height: {{isIphoneX?'85px':'55px'}};">
    <text class="reset" bindtap="resetMoreFilter">重置</text>
    <text bindtap="confirmMoreFilter">确定</text>
  </view>
</view>

<!-- capture-catch:touchmove="preventdefault" -->
